import { REPORT_SECTIONS } from "../../config/constants";
import { ReportType } from "../../global";
import { useLocation } from "react-router";
/* render strategies */
import { RenderProcessos } from "./renderProcessos.strategy";

export const getStrategyMap = (): Record<string, any> => {
  const location = useLocation();
  const reportType: ReportType | string = location.pathname.split("/")[2];
  // CPF
  const strategyProcessos = new RenderProcessos();

  // CPF e CNPJ
  const cpf_cnpj_map: Record<string, any> = {
    [REPORT_SECTIONS.processos]: strategyProcessos,
  };

  switch (reportType) {
    case "cnpj":
      return {
        ...cpf_cnpj_map
      };
    case "cpf":
      return {
        ...cpf_cnpj_map,
      }
    default:
      return {};
  }
};