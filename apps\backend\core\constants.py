from fastapi import HTTPException
from core.config import settings
from exceptions.business_exceptions import MissingMainReportError
class DefaultReports:
    DEFAULT_REPORTS_TO_FETCH = None 


class DefaultPageLogs:
    pagedefault=10

class DefaultPageInvites:
    pagedefault=10


class ReportMockValidator:
    TEST_VALUES_BY_TYPE = {
        'cpf': {'***********', '***********', '***********', '***********', '***********', '***********'},
        'cnpj': {'16670085000155', '05757597000218'},
        'telefone': {'***********', '***********','***********','***********','***********'},
        'email': {'<EMAIL>', '<EMAIL>','<EMAIL>'}
    }

    ERROR_VALUES_BY_TYPE = {
        'cpf': {'***********'}
    }

    @classmethod
    def is_test_case(cls, report_type: str, report_input_value: str) -> bool:
        return report_input_value in cls.TEST_VALUES_BY_TYPE.get(report_type, set())

    @classmethod
    def is_error_case(cls, report_type: str, report_input_value: str) -> bool:
        return report_input_value in cls.ERROR_VALUES_BY_TYPE.get(report_type, set())




class Endpoints:
# Endpoint suffixes
    STATUS_ENDPOINT_SUFFIX = "/status"
    RESULT_ENDPOINT_SUFFIX = "/result"
    REPORTS_ENDPOINT_SUFFIX = "/snap/reports"
    TELEFONE_ENDPOINT_SUFFIX = "/telefone"
    EMAIL_ENDPOINT_SUFFIX = "/email"
    CPF_ENDPOINT_SUFFIX = "/cpf"
    CNPJ_ENDPOINT_SUFFIX = "/cnpj"
    CREDITS_REMAINING_SUFFIX = "/remaining"
    base_url = settings.BASE_API_URL
    status_endpoint = base_url + STATUS_ENDPOINT_SUFFIX
    result_endpoint = base_url + RESULT_ENDPOINT_SUFFIX
    snap_report_enpoint = base_url + REPORTS_ENDPOINT_SUFFIX
    credits_report_endpoint = base_url + CREDITS_REMAINING_SUFFIX


class _ReportInputs:
    """Mapear entradas principais de reports aqui para o código ficar organizado"""
    cpf = 'cpf'
    cnpj = 'cnpj'
    email = 'email'
    telefone = 'telefone'
    documento_1 = 'documento_1'  # Para relações; CPF ou CNPJ
    documento_2 = 'documento_2'  # Para relações; CPF ou CNPJ
    relacoes = 'relacoes'        # Para relações; lista 2 de CPF/CNPJ
    nome = 'nome'
    cpf_parcial = 'cpf_parcial'
    sexo = 'sexo'
    uf = 'uf'
    nome_mae = 'nome_mae'
    idade_inicial = 'idade_inicial'
    idade_final = 'idade_final'
    reportType = "report_type"
    reportInputValue = "report_input_value"


class _ApiInputs:
    """
    Mapear inputs para API do SNAP aqui.
    Estes devem ser os nomes de campos conforme constam na documentação da API.
    """
    cpf = _ReportInputs.cpf
    cnpj = _ReportInputs.cnpj
    email = _ReportInputs.email
    telefone = _ReportInputs.telefone
    nome = _ReportInputs.nome
    uf = _ReportInputs.uf
    documento_1 = _ReportInputs.documento_1
    documento_2 = _ReportInputs.documento_2
    ignorar_outros_contatos = 'ignorar_outros_contatos'
    ignorar_consorcios = 'ignorar_consorcios'
    profundidade_relacoes = 'profundidade'
    ano = 'ano'
    titulo_eleitor = 'titulo_eleitor'
    termo = 'termo'
    chave_transparencia_br = 'chave'       # TODO ainda não está implementado, usado em Transparência BR
    orgao = 'orgao'                        # TODO ainda não está implementado, usado em Transparência AM
    mes_ano = 'data'                       # TODO ainda não está implementado, usado em Transparência MG
    municipio = 'municipio'                # TODO ainda não está implementado, usado em Transparência PR
    quadro_funcional = 'quadro_funcional'  # TODO ainda não está implementado, usado em Transparência PR
    razao_social = 'razao_social'


class _ApiOutputs:
    """
    Mapear outputs da API do SNAP aqui.
    Estes devem ser os nomes de campos conforme constam na documentação da API.
    """
    nome = 'full name'
    titulo_eleitor = 'titulo de eleitor'
    razao_social = 'razao social'
    search_args = 'search_args'

class TableNames:
    users='users'
    organizations='organizations'
    user_reports='user_reports'
    invite='invite'
    organization_users='organization_users'
    user_columns_hmac='user_columns_hmac'

class ReportTypes:
    cpf="cpf"
    cnpj="cnpj"
    telefone="telefone"
    email="email"
    combinado="combinado"
    relacao="relacoes"

class ErrorCodes:
# Error codes
    WAITING_RESULTS_CODE = 409
    NO_ACCESS_CODE = 401
    REQUEST_NOT_FOUND_CODE = 404
    SNAP_API_FORWARDING_ERROR = 400

class ReportStatus:
    class Success:
        complete = 'Complete'
        saved = "Saved"
        created = 'Created'
        success = '"SUCCESS"'
        successo = 'success'

    class InProgress:
        processing = 'Processing'
        created_empty_report = "Created Empty Report"
        pending = '"PENDING"'
        started = '"STARTED"'
        pending_low_case='pending'
        update_pending_count='Updated Report Status to pending and increase Type Count'

    class Failure:
        failed_created_empty_report = "Failed Create Empty Record"
        failed_commit_empty_report = "Failed Commit Empty Record"
        failed_save_user = "Fail to Save User"
        failed_save_report = "Fail to Save Report"
        failure = 'Failure'  # Just in case
        failed = 'failed'
    @classmethod
    def normalize(cls, status: str) -> str:
        if status in vars(cls.InProgress).values():
            return SummaryReportStatus.pending
        elif status in vars(cls.Success).values():
            return SummaryReportStatus.success
        elif status in vars(cls.Failure).values():
            return SummaryReportStatus.error
        else:
            return SummaryReportStatus.error



class JWTFields:
    sub='sub'
    email='email'
    realm_access='realm_access'
    roles='roles'


class SummaryReportStatus:
    pending='pending'
    success = 'success'
    failed = 'failed'
    error='error'

class Messages:
    # Messages
    MSG_KEY = "msg"
    SNAP_API_ERROR_MSG = "Erro ao consultar base."

class Timeouts: 
# Timeouts
    TIMEOUT_SECONDS = 1800

class Collections:
    """Nomes de Collections"""
    users = 'users'
    report_types = 'report_types'
    reports_by_user = 'user_reports'


class Fields:
    """Nomes de campos de documentos"""
    last_login = 'last_login'
    available_report_types = 'report_types'
    total_credits = 'credits'
    modified_at = 'modified_at'
    report_name = 'report_name'
    report_type = 'report_type'
    credits_used = 'creditsUsed'
    search_args = 'report_search_args'
    report_status = 'report_status'
    email = 'email'
    name = 'name'
    image = 'image'
    salt = 'salt'
    verifier = 'verifier'
    iv = 'iv'
    data = 'data'
    created_at ='created_at'
    omitted_notes = 'omitted_notes'
    user_id = "user_id"
    user_reports_id = "user_reports_id"
    report_search_args = "report_search_args"
    subject_name = "subject_name"
    subject_mother_name = "subject_mother_name"
    subject_age="subject_age"
    subject_sex="subject_sex"
    role = 'role'
    organization_id = 'organization_id'
    image_logo = 'image_logo'
    api_key = 'api_key'
    report_types = 'report_types'
    modified_by = 'modified_by'
    sent_at = 'sent_at'
    email_invited = 'email_invited'
    invite_valid_until= 'invite_valid_until'
    status = 'status'
    joined_at = 'joined_at'
    exited_at = 'exited_at'
    validate_until = 'validate_until'

class UserFields:
    credits_monthly='credits_monthly'
    next_reset_credits='next_reset_credits'


class InviteFields:
    invite_id = 'invite_id'
    user_sender_id = 'user_sender_id'
    organization_id = 'organization_id'
    status_invite = 'status_invite'
    type_invite = 'type_invite'
    report_types = 'report_types' 
    sent_at = 'sent_at' 
    email_invited = 'email_invited'
    invite_valid_until = 'invite_valid_until' 
    credits_sent = 'credits_sent'
    organization_name = 'organization_name'
    email_sender = 'email_sender'
    name_sender = 'name_sender'


class Status:
    ativo="ativo"
    inativo="inativo"


class InviteStatusCons:
    enviado = "enviado"
    negado = "negado"
    aceito = "aceito"
    cancelado = "cancelado"
    desvinculado= "desvinculado"


class InviteTypeCons:
    investigador = "investigador"
    administrador = "administrador"


class InviteParameters:
    invite_valid_until=15


class OrganizationUsersParameters:
    validate_until_days=365


class RolesDb:
    standAlone="standalone"
    investigador="investigador"
    administrador="administrador"


class Roles:
    standAlone =  RolesDb.standAlone + "-role"
    investigador = RolesDb.investigador +"-role"
    administrador = RolesDb.administrador +"-role"
    edit_user = "role-edit-user"
    create_invite_user = "role-invite-user"
    add_api_key = "role-add-api-key"
    create_report = "role-create-report"
    update_report = "role-update-report"
    view_report_list = "role-view-report-list"
    view_report_details = "role-view-report-details"
    get_user_logs = "role-get-user-logs"
    get_organization_logs = "role-get-organization-logs"
    answer_invite = "role-answer-invite"
    cancel_invite = "role-cancel-invite"
    get_organization_invite = "role-get-organization-invite"
    get_user_invite = "role-get-user-invite"
    get_data_from_user_invite = "role-get-data-from-user-invite"
    leave_organization = "role-leave-organization"
    remove_organization = "role-remove-organization"
    get_invite_details = "role-get-invite-details"



class KeycloakGroup:
    UserStandAlone= "UserStandAlone"
    UserInvestigador= "UserInvestigador"
    UserAdministrador= "UserAdministrador"
 

class ReportInputs:
    report_name = 'report_name'
    user_reports_id = 'user_reports_id'
    reports_to_fetch = 'reports_to_fetch'
    report_type = 'report_type'
    credits_used = 1


class ReportOutputs:
    message = 'msg'
    user_reports_id = ReportInputs.user_reports_id


class ReportMessages:
    started = 'Report generation started!'
    completed = 'Report generation completed!'
    updated = 'Report updated!'
    failure = 'Failed to create report!'
 

"""
Mapeamentos de classes para mensagens de erro
"""

class StatusOrganization:
    ativo="ativo"
    inativo="inativo"

class _ReportErrorMessage:
    """
    Classe auxiliar para ajudar a mapear erros no formato esperado.
    Aceita um código de status, mensagem e opcionalmente detalhes referentes ao erro.
    """
    def __init__(self, status_code: int, error: str, message: str):
        self.status_code = status_code
        self.error = error
        self.message = message

    def to_dict(self, message_format: str = None):
        message = self.message.format(message_format) if message_format else self.message
        return {
            "status_code": self.status_code,
            "detail": {
                "error": self.error,
                "message": message
            }
        }


class CustomErrorMessages:
    # Custom error messages using standard HTTP status codes
    no_access = _ReportErrorMessage(403, 'Forbidden', 'User does not have access to report of type "{}"!')
    unimplemented = _ReportErrorMessage(501, 'Not Implemented', 'Report type "{}" not yet supported')
    no_credits = _ReportErrorMessage(402, 'Payment Required', 'User does not have enough credits for report of type "{}"!')
    invalid_report_arguments = _ReportErrorMessage(400, 'Bad Request', 'Report must be generated with the following required arguments: {}')
    invalid_token = _ReportErrorMessage(401, 'Unauthorized', 'Invalid token')
    invalid_argument_report_type = _ReportErrorMessage(400, 'Bad Request', 'Must send "type" in request!')
    invalid_argument_missing_id = _ReportErrorMessage(400, 'Bad Request', 'Must provide "reportId" to collect a report!')
    invalid_argument_missing_field = _ReportErrorMessage(400, 'Bad Request', 'Must provide field "{}" for this request!')
    user_id_not_found_token = _ReportErrorMessage(400, 'Bad Request', 'User id not found on token!')
    user_not_found = _ReportErrorMessage(404, 'Not Found', 'User "{}" not found!')
    session_expired = _ReportErrorMessage(401, 'Unauthorized', 'Session expired. Please sign in again.')
    user_not_authenticated = _ReportErrorMessage(401, 'Unauthorized', 'User not authenticated!')
    token_verification_fail = _ReportErrorMessage(400, 'Bad Request', 'Token verification failed')
    invalid_argument_missing_omitted_notes = _ReportErrorMessage(400, 'Bad Request', 'Must provide "omitted_notes" to update a report!')
    timeout = _ReportErrorMessage(408, 'Request Timeout', 'Timeout during request to external API!')
    report_already_created = _ReportErrorMessage(409, 'Conflict', 'Report generation has already been started!')
    report_does_not_exist = _ReportErrorMessage(412, 'Precondition Failed', 'Blank report does not exist for report creation!')
    invalid_number_of_reports = _ReportErrorMessage(400, 'Bad Request', 'Must send at least 2 report_ids for merge!')
    invalid_report_id = _ReportErrorMessage(400, 'Bad Request', 'Report with ID {} does not exist!')
    invalid_report_types = _ReportErrorMessage(400, 'Bad Request', 'Can only merge reports of different types!')
    missing_main_report = _ReportErrorMessage(400, 'Bad Request', 'Must send CPF or CNPJ report for merge!')
    field_not_array = _ReportErrorMessage(400, 'Bad Request', 'Must send "{}" as an array!')
    spend_quota_failed = _ReportErrorMessage(500, 'Internal Server Error', 'Failed to spend quota due to an internal error.')
    fail_fetch_jwk = _ReportErrorMessage(401, 'Unauthorized', 'Failed to fetch JWKs')
    snap_api_failed = _ReportErrorMessage(502, 'Bad Gateway', 'Snap API returned an unexpected status code: {}')
    input_value_snap_wrong = _ReportErrorMessage(422, 'Unprocessable Entity', 'Dados inseridos inválidos.')
    problems_with_snap_api = _ReportErrorMessage(500, 'Internal Server Error', 'Problems with the snap api, please try again later.')
    snap_api_no_id = _ReportErrorMessage(500, 'Internal Server Error', 'Snap API did not return a request ID.')
    no_context_for_client = _ReportErrorMessage(400, 'Bad Request', 'No context found for client_id "{}".')
    fail_refresh_token = _ReportErrorMessage(401, 'Unauthorized', 'Could not refresh token')
    missing_auth_code = _ReportErrorMessage(400, 'Bad Request', 'Missing authorization code')
    logout_fail_keycloak = _ReportErrorMessage(401, 'Unauthorized', 'Logout failed in Keycloak.')
    missing_refresh_token = _ReportErrorMessage(400, 'Bad Request', 'Missing refresh token')
    verifier_cannot_be_update = _ReportErrorMessage(400, 'Bad Request', 'Fail to insert verifier on user id {}')
    fail_add_one_to_pending = _ReportErrorMessage(400, 'Bad Request', 'Failed to add one report pending and type to user.')
    fail_create_empty_report = _ReportErrorMessage(400, 'Bad Request', 'Failed to create empty record.')
    fail_update_error_report = _ReportErrorMessage(400, 'Bad Request', 'Failed to update error report.')
    fail_to_access_users_reports = _ReportErrorMessage(400, 'Bad Request', 'Failed to get users reports.')
    fail_to_access_data_to_get_pending_reports = _ReportErrorMessage(400, 'Bad Request', 'Fail to access data to get pending reports')
    fail_to_access_data_to_get_number_of_report_type = _ReportErrorMessage(400, 'Bad Request', 'Fail to access data to get number of report type')
    fail_to_fetch_data_from_snap_api = _ReportErrorMessage(500, 'Internal Server Error', 'Failed to fetch data from Snap API after multiple attempts.') 


class MergedReport:
    REPORT_TYPE = 'combinado'

    def __init__(self, *report_data):

        report_data = list(report_data)
        report_type_list = [report[Fields.report_type] for report in report_data]
        self.main_report_data = self.main_report_class = None
        for index, (report_data_dict, report_type) in enumerate(zip(report_data, report_type_list)):
            # report_class = AVAILABLE_REPORTS[report_type]
            # if not issubclass(report_class, DocReport):
            #     continue

            self.main_report_data = report_data.pop(index)
            # self.main_report_class = report_class
            break

        if self.main_report_data is None or self.main_report_class is None:
            raise MissingMainReportError()

        self.other_reports = report_data