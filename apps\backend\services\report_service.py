import logging
import math
from datetime import datetime, timezone

import httpx
from fastapi import HTTP<PERSON>x<PERSON>, FastAPI
from fastapi.responses import J<PERSON><PERSON>esponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, asc, desc, func, and_, case, update, literal, distinct
from sqlalchemy.dialects.postgresql import insert, JSONB
from sqlalchemy.exc import SQLAlchemyError
from typing import List, Optional, Tuple

from core.constants import (Fields,
                            Endpoints, UserFields,
                            DefaultPageLogs, SummaryReportStatus)

from models.report_model import UserReports
from models.user_columns_hmac import UserColumnsHmac
from models.user_model import Users

from schemas.report_schema import SnapApiRequest, InsertReport
from schemas.user_schema import PaginatedResponse, PaginationMetadata

from services.base_service import BaseService
from services.SnapStatusTracker import SnapStatusTracker
from services.apikey_service import ApikeyService
from services.credits_service import CreditsService
from services.user_service import UserStandaloneService
from services.organization_users_service import OrganizationUsersService
from services.websocket import try_send_websocket

from utils.db_utils import retry_db_operation
from exceptions.business_exceptions import (
    ReportPermissionDeniedError,
    ReportApiKeyMissingError,
    ReportInsufficientCreditsError,
    FailToAccessUsersReportsError,
    InputValueSnapWrongError,
    ProblemsWithSnapApiError,
    SnapApiFailedError,
    SnapApiNoIdError,
    FailToAccessDataToGetNumberOfReportTypeError,
    FailCreateEmptyReportError,
    FailUpdateErrorReportError,
    FailToAccessDataToGetPendingReportsError
)

logger = logging.getLogger(__name__)



class UserReportsService(BaseService):
    def __init__(self, db: AsyncSession, user_id: str = None, user_reports_id: str = None, organization_id: str = None) -> None:
        super().__init__(db)
        
        self.user_data = None
        self.user_id = user_id
        self.organization_id = organization_id
        self.user_reports_id = user_reports_id
        self.api_key = None


    def set_user_data(self, user_data: dict):
        self.user_data=user_data


    def set_api_key(self, api_key: str):
        return super().set_api_key(api_key)


    async def _apply_contextual_filters_to_query(self, query, is_user_scope: bool):
        """
        Applies contextual filters to a query based on the scope.

        :param query: The SQLAlchemy query object to modify.
        :param is_user_scope: If True, filters for the current user's context (personal or within their org).
                              If False, filters for the entire organization's context.
        """
        logger.info(
            "[_apply_contextual_filters_to_query] Applying filters for user %s with scope: %s",
            self.user_id, "user" if is_user_scope else "organization"
        )
        if is_user_scope:
            # Personal scope: filter by user_id and their organization context.
            query = query.where(UserReports.user_id == self.user_id)
            logger.debug("[_apply_contextual_filters_to_query] Applied user_id filter: %s", self.user_id)

            organization_user_service = OrganizationUsersService(db=self.db, user_id=self.user_id)
            user_organization_data = await organization_user_service.get_organization_user()
            if not user_organization_data:
                # Standalone user: only reports with no organization_id
                logger.info("[_apply_contextual_filters_to_query] User %s is standalone. Filtering for reports with no organization_id.", self.user_id)
                query = query.where(UserReports.organization_id.is_(None))
            else:
                # User in an org: only reports for that user within that org
                organization_id = user_organization_data.organization_id
                logger.info("[_apply_contextual_filters_to_query] User %s is in organization %s. Applying organization_id filter.", self.user_id, organization_id)
                query = query.where(UserReports.organization_id == organization_id)
        else:
            # Organization scope: filter by organization_id.
            if not self.organization_id:
                logger.warning("[_apply_contextual_filters_to_query] Organization scope requested but no organization_id is set for the service.")
                return query.where(literal(False)) # Return no results if org ID is missing
            logger.info("[_apply_contextual_filters_to_query] Applying organization scope filter for organization_id: %s", self.organization_id)
            query = query.where(UserReports.organization_id == self.organization_id)
        return query


    async def get_all_logs(self,
                           column_order: str,
                           report_type: str = None,
                           limit:int = DefaultPageLogs.pagedefault , 
                           page:int = 1, order:str = "desc",
                            is_user: bool = True, created_start_at: datetime = None,
                            created_end_at:datetime = None):
        """
        Id can be from the organization or from the user
        Returns paginated response with data and pagination metadata
        """

        # Base query for data
        base_query = select(
            UserReports.created_at,
            UserReports.user_id,
            UserReports.report_type,
            UserReports.user_reports_id,
            Users.name,
            Users.role,
            Users.email
        ).join(Users, UserReports.user_id == Users.user_id)

        # Count query for total items
        count_query = select(func.count(UserReports.user_reports_id)).join(Users, UserReports.user_id == Users.user_id)

        # Apply contextual filters based on user or organization scope
        base_query = await self._apply_contextual_filters_to_query(base_query, is_user_scope=is_user)
        count_query = await self._apply_contextual_filters_to_query(count_query, is_user_scope=is_user)

        # Apply common filters to both queries
        common_filters = []
        if report_type:
            common_filters.append(UserReports.report_type == report_type)
        if created_start_at:
            common_filters.append(UserReports.created_at >= created_start_at)
        if created_end_at:
            common_filters.append(UserReports.created_at <= created_end_at)

        if common_filters:
            base_query = base_query.where(and_(*common_filters))
            count_query = count_query.where(and_(*common_filters))

        page = max(1, page)
        offset = (page - 1) * limit if limit else None

        column_map = {
            "created_at": UserReports.created_at
        }

        user_or_org_id = self.user_id if is_user else self.organization_id
        user_or_org = "user" if is_user else "organization"
        if column_order not in column_map:
            logger.warning("[get_all_logs][%s(%s)] Invalid column_order: '%s', defaulting to 'created_at'", user_or_org, user_or_org_id, column_order)

        sort_column = column_map.get(column_order, UserReports.created_at)
        sort_func = asc if order.lower() == "asc" else desc

        base_query = base_query.order_by(sort_func(sort_column))

        if limit:
            base_query = base_query.limit(limit)
            if offset:
                base_query = base_query.offset(offset)
            logger.info("[get_all_logs][%s(%s)] Limit: %s | Page: %s | Offset: %s", user_or_org, user_or_org_id, limit, page, offset)

        try:
            # Execute both queries
            result = await self.db.execute(base_query)
            rows = result.mappings().all()

            count_result = await self.db.execute(count_query)
            total_items = count_result.scalar()

            # Calculate pagination metadata
            total_pages = math.ceil(total_items / limit) if limit > 0 else 1
            has_next = page < total_pages
            has_previous = page > 1

            data = [dict(r) for r in rows]

            pagination_metadata = PaginationMetadata(
                current_page=page,
                total_pages=total_pages,
                total_items=total_items,
                page_size=limit,
                has_next=has_next,
                has_previous=has_previous
            )

            logger.info("[get_all_logs][%s(%s)] Found %s reports. Total: %s, Page: %s/%s",
                       user_or_org, user_or_org_id, len(rows), total_items, page, total_pages)

            return PaginatedResponse(data=data, pagination=pagination_metadata)
        except Exception as e:
            logger.error("[get_all_logs][%s(%s)] Error executing query: %s", user_or_org, user_or_org_id, e)
            raise


    async def validate_access_to_report(self, report_type: str):

        user_service = UserStandaloneService(db=self.db, user_id=self.user_id)
        user_data = await user_service.get_user_data()

        user_report_types = user_data.report_types
        if report_type not in user_report_types:
            logger.warning("[validate_access_to_report] No permission of type %s for user: %s", 
                        report_type , self.user_id)
            raise ReportPermissionDeniedError()
            

        api_key_service = ApikeyService(db=self.db, user_id=self.user_id)
        self.api_key = await api_key_service.get_api_key()

        if self.api_key is None:
            logger.warning("[validate_access_to_report] No api_key for user: %s", 
                            self.user_id)
            raise ReportApiKeyMissingError()

        credits_service = CreditsService(db=self.db, user_id=self.user_id)
        credits_service.set_api_key(api_key=self.api_key)

        user_data_dict = {Fields.total_credits: user_data.credits,
                          UserFields.credits_monthly: user_data.credits_monthly,
                          UserFields.next_reset_credits: user_data.next_reset_credits
                          }

        minimun_credits, db_credits = await credits_service.compare_user_credits_with_api_credits(user_data_dict)


        if minimun_credits<=0:
            logger.warning("[validate_access_to_report] No credits for user: %s", self.user_id)
            raise ReportInsufficientCreditsError()
        if db_credits:
            await credits_service.change_user_credits(credit_delta=-1)
        
        return self.api_key
    

    async def get_saved_reports_handler(self, 
                                        limit: int,  page: int,
                                        order: str, column_order: str, 
                                        hmac_filter: str, hmac_column: str):
        
        logger.info("[get_saved_reports_handler] Fetching saved reports")

        try:

            return await retry_db_operation(
                lambda: self.list_user_reports(
                    limit=limit,
                    page=page,
                    order=order,
                    column_order=column_order, 
                    hmac_filter=hmac_filter,
                    hmac_column=hmac_column
                )
            )

        except Exception as e:
            logger.error("[get_saved_reports_handler] Failed: %s", e)
            raise FailToAccessUsersReportsError()
        

    async def list_user_reports(
        self,
        limit: Optional[int] = None,
        page: int = 1,
        order: str = "desc",
        column_order: str = "modified_at",
        hmac_filter: Optional[str] = None,
        hmac_column: Optional[str] = None,
        offset: Optional[int] = None
    ) -> List[dict]:
        """
        List user reports with pagination and optional HMAC filtering.
        """
        logger.info("[list_user_reports][user(%s)] Listing user reports.", self.user_id)

        user_reports_ids = False
        if hmac_filter:
            try:
                user_reports_ids = await self.filter_using_hmac(hmac_filter=hmac_filter, hmac_column=hmac_column)
            except Exception as e:
                logger.error("[list_user_reports][user(%s)] Error while filtering with HMAC: %s", self.user_id, e)
                raise

        query = select(
            UserReports.report_name,
            UserReports.report_status,
            UserReports.modified_at,
            UserReports.created_at,
            UserReports.report_type,
            UserReports.report_search_args,
            UserReports.user_reports_id,
            UserReports.subject_age,
            UserReports.subject_mother_name,
            UserReports.subject_name,
            UserReports.subject_sex
        )

        if user_reports_ids:
            query = query.where(UserReports.user_reports_id.in_(user_reports_ids))
        elif hmac_filter: # If a filter was provided but no IDs were found
            return []

        query = await self._apply_contextual_filters_to_query(query, is_user_scope=True)

        page = max(1, page)
        if offset is None or offset < 0:
            offset = (page - 1) * limit if limit else None

        column_map = {
            "created_at": UserReports.created_at,
            "modified_at": UserReports.modified_at,
            "report_name": UserReports.report_name,
        }
        if column_order not in column_map:
            logger.warning("[list_user_reports][user(%s)] Invalid column_order: '%s', defaulting to 'created_at'", self.user_id, column_order)

        sort_column = column_map.get(column_order, UserReports.created_at)
        sort_func = asc if order.lower() == "asc" else desc

        # Use CASE WHEN to prioritize 'pending' report_status (when it's a valid JSON)
        pending_first = case(
            (UserReports.report_status["status_report"].astext == "pending", 0),
            else_=1
        )

        query = query.order_by(pending_first, sort_func(sort_column))

        if limit:
            query = query.limit(limit)
            if offset:
                query = query.offset(offset)
            logger.info("[list_user_reports][user(%s)] Limit: %s | Page: %s | Offset: %s", self.user_id, limit, page, offset)

        try:
            result = await self.db.execute(query)
            rows = result.mappings().all()
            logger.info("[list_user_reports][user(%s)] Found %s reports.", self.user_id, len(rows))
            return [dict(r) for r in rows]
        except Exception as e:
            logger.error("[list_user_reports][user(%s)] Error executing query: %s", self.user_id, e)
            raise


    async def populate_report_handler(self, body: InsertReport):

        logger.info("[populate_report_handler][user(%s)] Populating report with data", self.user_id)

        await self._populate_report_data(
            report_name=body.report_name,
            report_type=body.report_type,
            report_status=body.report_status,
            report_search_args=body.report_search_args,
            subject_name=body.subject_name,
            subject_mother_name=body.subject_mother_name,
            subject_age=body.subject_age,
            subject_sex=body.subject_sex,
            created_at=body.created_at,
            modified_at=body.modified_at,
            omitted_notes=body.omitted_notes,
            data=body.data
        )

        if body.hmac: #TODO REMOVE THIS CONDITIONAL WHEN WE START TO USE
            await self.insert_hmacs(hmac=body.hmac)


    async def _populate_report_data(self,
                                report_name: dict, report_type: str, report_status: dict, 
                                report_search_args: dict,
                                subject_name: dict, subject_mother_name: dict, 
                                subject_age: int, subject_sex: dict,
                                created_at: str, modified_at: str, omitted_notes: dict, data: dict):
    
        logger.info("[_populate_report_data][user(%s)] Populating report with full data.", self.user_id)
        try:
            update_dict = {
                Fields.report_name: report_name,
                Fields.report_type: report_type,
                Fields.report_status: report_status,
                Fields.report_search_args: report_search_args,
                Fields.subject_name: subject_name,
                Fields.subject_mother_name: subject_mother_name,
                Fields.subject_age: subject_age,
                Fields.subject_sex: subject_sex,
                Fields.created_at: created_at,
                Fields.modified_at: modified_at,
                Fields.omitted_notes: omitted_notes,
                Fields.data: data,
            }

            await self.db.execute(
                update(UserReports)
                .where(UserReports.user_reports_id == self.user_reports_id)
                .where(UserReports.user_id == self.user_id)
                .values(**update_dict)
            )
            await self.db.commit()
            logger.info("[update_blank_report][user(%s)] Blank report updated successfully.", self.user_id)
        except Exception as e:
            logger.error("[update_blank_report][user(%s)] Error: %s", self.user_id, e)
            await self.db.rollback()



    async def get_one_report_handler(self):
        logger.info("[get_one_report_handler][user(%s)] Fetching one report", self.user_id)

        result = await self.db.execute(
            select(UserReports)
            .where(UserReports.user_id == self.user_id)
            .where(UserReports.user_reports_id == self.user_reports_id)
        )

        report = result.scalars().first()
        logger.info("[get_report] Fetched report: %s", report)
        return report


    async def get_data_from_snap_api_handler(self, body: SnapApiRequest) -> JSONResponse:
        logger.info("[get_data_from_snap_api_handler][user(%s)] Fetching data from SNAP API", self.user_id)

        headers = {
            'Ocp-Apim-Subscription-Key': self.api_key,
            'Accept': 'application/json'
        }

        #extract from user the group and access allowed)
        report_type = body.report_type

        request_id = ''

        request_payload = {report_type: body.report_input_value}

        # Log the outgoing request details
        logger.info("[get_data_from_snap_api_handler][user(%s)] Sending request to SNAP API endpoint: %s", self.user_id, Endpoints.snap_report_enpoint + "/" + report_type)
        logger.info("[get_data_from_snap_api_handler][user(%s)] Request headers: %s", self.user_id, headers)
        logger.info("[get_data_from_snap_api_handler][user(%s)] Request payload: %s", self.user_id, request_payload)

        async with httpx.AsyncClient() as client:
            response = await client.post(Endpoints.snap_report_enpoint + "/" + report_type, headers=headers, json=request_payload)

        if response.status_code==422:
            logger.error("[get_data_from_snap_api_handler][user(%s)] Input value format wrong", self.user_id)
            raise InputValueSnapWrongError()
        
        if response.status_code==500:
            logger.error("[get_data_from_snap_api_handler][user(%s)] Problems with snap api", self.user_id)
            raise ProblemsWithSnapApiError()

        if response.status_code not in [200, 202]:
            logger.error("[get_data_from_snap_api_handler][user(%s)] Snap API failed", self.user_id)
            raise SnapApiFailedError(response.status_code)

        request_id = response.json().get('id')
        if not request_id:
            logger.error("[get_data_from_snap_api_handler][user(%s)] Snap API returned no ID", self.user_id)
            raise SnapApiNoIdError()

        return {"id": request_id}


    async def snap_status_ws(self, app: FastAPI, request_id: str, report_type: str,
                            report_number: str, report_search_args: dict,
                            api_key: str) -> None:
        """
        Track SNAP status for a report request.

        Args:
            app: FastAPI application instance
            request_id: SNAP API request identifier
            user_id: User identifier
            report_type: Type of report being processed
            report_number: Report number
            report_search_args: Search arguments for the report
            reports_id: Internal report identifier
        """
        tracker = SnapStatusTracker(app, self.user_id, self.user_reports_id, api_key)
        await tracker.track_status(request_id, report_type, report_number, report_search_args)


    async def handle_max_retries_exceeded(self,
                                        app, client_id, user_reports_id, 
                                        parsed_body, exception):
            
        """Handle case when max retries are exceeded during Snap API requests."""
        logger.error(
            f"[get_data_from_snap_api][user({self.user_id})] All retry attempts failed: {str(exception)}"
        )

        # Handle server errors from snap API differently
        if isinstance(exception, HTTPException) and exception.status_code == 500:
            # Create report with error status for server-side failures
            report_status = {"status_report": SummaryReportStatus.error}
            user_reports_id = await self.create_blank_report_with_status(
                snap_request_id=client_id,
                status_report=report_status,
                report_type=parsed_body.report_type,
                report_input_encrypted=parsed_body.report_input_encrypted
            )
        else:
            # Update existing report to error state
            if user_reports_id:
                await self.update_error_report(
                    snap_request_id=client_id
                )
                logger.info(
                    f"[get_data_from_snap_api][user({self.user_id})] Updated status to error for report {user_reports_id}"
                )

        # Send websocket error notification if possible
        try:
            connection_manager = app.state.connection_manager
            await try_send_websocket(
                connection_manager,
                self.user_id,
                user_reports_id,
                {
                    "id": user_reports_id,
                    "status_code": SummaryReportStatus.error
                }
            )
            logger.info(
                f"[get_data_from_snap_api][user({self.user_id})] Sent error via WebSocket for report {user_reports_id}"
            )
        except Exception as ws_error:
            logger.error(
                f"[get_data_from_snap_api][user({self.user_id})] Failed to send WebSocket error: {str(ws_error)}"
            )

        # Raise appropriate exception to caller
        raise FailToAccessDataToGetNumberOfReportTypeError()


    async def create_blank_report_with_status(self, status_report: dict, report_type: str, report_input_encrypted: dict = None):
        """Create a blank report with status"""
        logger.info("[create_blank_report_with_status][user(%s)] Creating blank report.", self.user_id)
        
        try:
            new_report = UserReports(
                user_id=self.user_id,
                created_at=datetime.now(timezone.utc),
                modified_at=datetime.now(timezone.utc),
                report_status=status_report,
                report_type=report_type,
                report_search_args=report_input_encrypted,
                organization_id=self.organization_id,
                is_deleted=False
            )
            self.db.add(new_report)
            await self.db.commit()
            await self.db.refresh(new_report)
            
            # Update the service's user_reports_id with the new report
            self.user_reports_id = new_report.user_reports_id
            
            logger.info("[create_blank_report_with_status][user(%s)] Blank report created successfully.", self.user_id)
            return new_report.user_reports_id
        except SQLAlchemyError as e:
            logger.error("[create_blank_report_with_status][user(%s)] Database error: %s", self.user_id, e)
            await self.db.rollback()
            raise FailCreateEmptyReportError()


    async def update_error_report_to_pending(self, status_report: dict):
        """Update error report to pending status"""
        logger.info("[update_error_report_to_pending][user(%s)] Updating error report to pending.", self.user_id)
        
        if not self.user_reports_id:
            logger.error("[update_error_report_to_pending][user(%s)] No report ID provided", self.user_id)
            raise ValueError("Report ID is required")
            
        try:
            update_query = (
                update(UserReports)
                .where(UserReports.user_reports_id == self.user_reports_id)
                .where(UserReports.user_id == self.user_id)
                .values({Fields.report_status: status_report, 
                         Fields.created_at: datetime.now(timezone.utc)})
            )
            
            await self.db.execute(update_query)
            await self.db.commit()
            logger.info("[update_error_report_to_pending][user(%s)] Updated error report to pending.", self.user_id)
        except SQLAlchemyError as e:
            logger.error("[update_error_report_to_pending][user(%s)] Database error: %s", self.user_id, e)
            await self.db.rollback()
            raise FailCreateEmptyReportError()


    async def update_error_report(self, snap_request_id: str):
        """Update report status to error"""
        logger.info("[update_error_report][user(%s)] Updating report to error status.", self.user_id)
        
        if not self.user_reports_id:
            logger.error("[update_error_report][user(%s)] No report ID provided", self.user_id)
            raise ValueError("Report ID is required")

        try:
            report_status = {
                "status_report": SummaryReportStatus.error,
                "snap_request_id": snap_request_id
            }

            await self.db.execute(
                update(UserReports)
                .where(UserReports.user_reports_id == self.user_reports_id)
                .where(UserReports.user_id == self.user_id)
                .values({Fields.report_status: literal(report_status, type_=JSONB), 
                         Fields.created_at: datetime.now(timezone.utc)})
            )
            await self.db.commit()

            logger.info("[update_error_report][user(%s)] Report updated to error.", self.user_id)
        except Exception as e:
            logger.error("[update_error_report][user(%s)] Failed to update report: %s", self.user_id, e)
            raise FailUpdateErrorReportError()
        

    async def get_pending_reports(self) -> List[Tuple[str, str, dict]]:
        """Get pending reports for a user"""
        logger.info(f"[get_pending_reports][user({self.user_id})] Fetching pending reports...")

        try:
            report_status = {"status_report": SummaryReportStatus.pending}

            result = await self.db.execute(
                select(
                    UserReports.user_reports_id,
                    UserReports.report_type,
                    UserReports.report_status
                )
                .where(UserReports.user_id == self.user_id)
                .where(UserReports.report_status.contains(report_status))
            )

            reports = result.fetchall()

            if not reports:
                logger.info(f"[get_pending_reports][user({self.user_id})] No pending reports found.")
                return []

            logger.info(f"[get_pending_reports][user({self.user_id})] Found {len(reports)} pending reports.")
            return reports
        except Exception as e:
            logger.error(f"[get_pending_reports][user({self.user_id})] Error: {e}")
            raise FailToAccessDataToGetPendingReportsError()


    async def get_number_of_report_type(self, report_type: str) -> str:
        """Get number of reports by type"""
        logger.info("[get_number_of_report_type][user(%s)] Getting number of types of a report for a user.", self.user_id)
        
        result = await self.db.execute(
            select(func.count())
            .where(UserReports.user_id == self.user_id)
            .where(UserReports.report_type == report_type)
        )
        count = result.scalar_one_or_none() or 0

        return str(count)


    async def insert_hmacs(self, hmac: dict):
        """Insert HMACs for a report"""
        if not self.user_reports_id:
            logger.error("[insert_hmacs] No user_reports_id provided")
            raise ValueError("user_reports_id is required")
            
        try:
            logger.info("[insert_hmacs] Inserting hmacs for user_reports_id: %s", self.user_reports_id)

            rows_to_insert = []
            for column_name, hmac_list in hmac['n-grams'].items():
                for hmac_value in set(hmac_list):
                    rows_to_insert.append({
                        'hmac': hmac_value,
                        'column_name': column_name,
                        'user_reports_id': self.user_reports_id
                    })

            if rows_to_insert:
                stmt = insert(UserColumnsHmac).values(rows_to_insert).on_conflict_do_nothing()
                await self.db.execute(stmt)
                await self.db.commit()

            logger.info("[insert_hmacs] Successfully inserted hmacs for user_reports_id: %s", self.user_reports_id)

        except Exception as e:
            logger.error("[insert_hmacs] Error inserting hmacs for user_reports_id %s: %s", self.user_reports_id, e)
            await self.db.rollback()
            raise


    async def filter_using_hmac(self, hmac_filter: str, hmac_column: str = None) -> List[str]:
        """Filter reports using HMAC"""
        user_reports_ids = []

        if hmac_filter:
            filters = []
            if hmac_filter:
                filters.append(UserColumnsHmac.hmac == hmac_filter)
            if hmac_column:
                filters.append(UserColumnsHmac.column_name == hmac_column)

            try:
                stmt = select(distinct(UserColumnsHmac.user_reports_id)).where(and_(*filters))
                result = await self.db.execute(stmt)
                user_reports_ids = [row[0] for row in result.fetchall()]
                logger.info("[filter_using_hmac] Fetched report_ids using hmac filter: %s | column: %s => %s", 
                           hmac_filter, hmac_column, user_reports_ids)

                if not user_reports_ids:
                    logger.warning("[filter_using_hmac] No user_reports_id found with given filters: hmac=%s, column=%s", 
                                  hmac_filter, hmac_column)
                    return []

            except Exception as e:
                logger.error("[filter_using_hmac] Error executing HMAC filter query: %s", e)
                raise

        return user_reports_ids


    async def get_data_to_pending_reports(self, request_snap_id: str) -> Tuple[str, str, dict]:
        """Get data for pending reports"""
        logger.info("[get_data_to_pending_reports][user(%s)] Getting data to pending report.", self.user_id)
        
        try:
            report_status = {"status_report": SummaryReportStatus.pending, "snap_request_id": request_snap_id}

            result = await self.db.execute(
                select(UserReports.report_type, UserReports.user_reports_id, UserReports.report_status)
                .where(UserReports.report_status == literal(report_status, type_=JSONB))
                .where(UserReports.user_id == self.user_id)
            )

            user_reports = result.fetchone()

            if user_reports:
                return user_reports.report_type, user_reports.user_reports_id, user_reports.report_status
            else:
                logger.warning("[get_data_to_pending_reports][user(%s)] No report pending found.", self.user_id)
                return '', '', {}

        except Exception as e:
            logger.error("[get_data_to_pending_reports][user(%s)] Error: %s", self.user_id, e)
            return '', '', {}


    async def insert_started_snap_status(self, status_report: dict):
        """Insert started snap status"""
        logger.info("[insert_started_snap_status][user(%s)] Inserting snap_status_ws already started.", self.user_id)
        
        try:
            query = select(UserReports.user_reports_id).where(
                UserReports.report_status == literal(status_report, type_=JSONB),
                UserReports.user_id == self.user_id
            )
            result = await self.db.execute(query)
            user_report_id = result.scalar_one_or_none()

            if user_report_id:
                logger.info("[insert_started_snap_status][user(%s)] Report found, updating report_status...", self.user_id)

                new_status = {**status_report, "running": True}

                update_query = (
                    update(UserReports)
                    .where(UserReports.user_reports_id == user_report_id)
                    .values(report_status=new_status)
                )
                await self.db.execute(update_query)
                await self.db.commit()
                
                # Update the service's user_reports_id with the found report
                self.user_reports_id = user_report_id
            else:
                logger.warning("[insert_started_snap_status][user(%s)] No matching report found.", self.user_id)

        except Exception as e:
            logger.error("[insert_started_snap_status][user(%s)] Error: %s", self.user_id, e)
