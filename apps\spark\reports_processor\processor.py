import json
from datetime import datetime, timezone
from io import BytesIO

import boto3
import botocore.exceptions
from pyspark.sql import SparkSession

import reports_processor.constants
from reports_processor.encoder import SetEncoder
from reports_processor.extractors import extract_entities_from_dataframe
from reports_processor.constants import ReportKeys, MINIO_ENDPOINT, MINIO_ACCESS_KEY, MINIO_SECRET_KEY, \
    logger, Constants, ReportType
# from apps.spark.reports_processor.formatters import organize_main_entity
from reports_processor.utils import normalize_document, get_constant


def process_report_data(data, report_type, search_value):
    """
    Process report data to transform it into the desired format.
    Returns a dictionary with the processed data.
    """
    # Determine entity type based on report type
    try:
        report_type = ReportType(report_type)
    except ValueError:
        logger.error(f"[process_report_data] Unknown report type: {report_type}")
        return None

    # Extract the data for this report type
    try:
        data_inside = data[ReportKeys.DATA][report_type.value][0]
    except KeyError:
        logger.error(f"[process_report_data] Missing data for report type {report_type}")
        return None

    # Convert to DataFrame
    json_data = json.dumps(data_inside)
    rdd = reports_processor.constants.spark.sparkContext.parallelize([json_data])
    df = reports_processor.constants.spark.read.option("multiline", "true").json(rdd)

    # Process entities
    extracted_entities = extract_entities_from_dataframe(df, report_type, normalize_document(search_value))

    # processed_data = organize_main_entity(
    #     extracted_entities, entity_type, report_type, search_value
    # )

    return extracted_entities


def handle_batch(df, epoch_id):
    """Processes each batch of data from the stream."""
    logger.info(f"[handle_batch] Handling batch {epoch_id}")

    s3 = boto3.client(
        "s3",
        endpoint_url=MINIO_ENDPOINT,
        aws_access_key_id=MINIO_ACCESS_KEY,
        aws_secret_access_key=MINIO_SECRET_KEY
    )

    rows = df.collect()
    for row in rows:
        try:
            logger.info(f"[handle_batch] Processing file: bucket={row['bucket']}, key={row['key']}")
            obj = s3.get_object(Bucket=row['bucket'], Key=row['key'])
        except botocore.exceptions.ClientError as e:
            if e.response['Error']['Code'] == 'NoSuchKey':
                logger.error(f"[handle_batch] File not found: {row['bucket']}/{row['key']}. Skipping...")
                continue
            else:
                raise

        final_result = process_file(obj['Body'].read().decode('utf-8'))

        if not final_result:
            continue

        # Convert to JSON and save
        # with open("output.json", "w", encoding="utf-8") as f:
        #     json.dump(dfinal, f, cls=SetEncoder, indent=4, ensure_ascii=False)

        json_result = json.dumps(final_result, cls=SetEncoder)
        timestamp = datetime.now(timezone.utc).strftime("%Y%m%d%H%M%S%f")
        file_tmp_path = f'/tmp/processed-at{timestamp}'

        with open(file_tmp_path, 'w') as f:
            f.write(json_result)

        # Upload to MinIO
        byte_stream = BytesIO(json_result.encode('utf-8'))
        s3.put_object(
            Bucket="processed-reports",
            Key=row['key'],
            Body=byte_stream
        )

        logger.info(f"[handle_batch] Successfully processed and uploaded: {row['key']}")


def process_file(file_content):
    # Read and preprocess file content
    # file_content = obj['Body'].read().decode('utf-8')
    original_data = json.loads(file_content)

    # Apply text substitutions from constants
    substitutions = get_constant(Constants.Generic, "translateWord", {})
    for old_text, new_text in substitutions.items():
        file_content = file_content.replace(old_text, new_text)

    # Parse the transformed content
    transformed_data = json.loads(file_content)

    # Extract metadata from original report
    result = {
        "report_name": original_data.get("report_name"),
        "report_type": original_data.get("report_type"),
        "user_reports_id": original_data.get("user_reports_id"),
        "report_status": original_data.get("report_status"),
        "report_search_args": original_data.get("report_search_args"),
        "subject_name": original_data.get("subject_name"),
        "subject_mother_name": original_data.get("subject_mother_name"),
        "subject_age": original_data.get("subject_age"),
        "subject_sex": original_data.get("subject_sex"),
        "created_at": original_data.get("created_at"),
        "modified_at": original_data.get("modified_at"),
        "omitted_notes": original_data.get("omitted_notes"),
    }

    # Get report type and search parameters
    report_type = transformed_data.get(ReportKeys.REPORT_TYPE)
    search_args = transformed_data.get(ReportKeys.SEARCH_ARGS, {})

    if not search_args:
        logger.error(f"[handle_batch] Missing search arguments in data. Skipping...")
        return None

    # TODO this will work only when we have 1 argument, para relações da ruim
    value = search_args.get(report_type)
    search_value = value if isinstance(value, str) else value[0] if isinstance(value, list) and value else None

    if not search_value:
        logger.error(f"[handle_batch] Missing search value for report type {report_type}. Skipping...")
        return None

    # Process the report data
    processed_data = process_report_data(transformed_data, report_type, search_value)
    if not processed_data:
        logger.error(f"[handle_batch] Missing processed data. Skipping report...")
        return None

    # Assemble final result
    result[ReportKeys.DATA] = {report_type: processed_data}
    final_result = {"data": result}

    return final_result


def debug_processing():
    # Initialize Spark in local mode
    reports_processor.constants.spark = SparkSession.builder \
        .appName("ReportProcessor-Debug") \
        .master("local[*]") \
        .getOrCreate()

    # Sample test data
    # test_data = '/Users/<USER>/Downloads/reportsEmailMultipleresponse_1749427931872.json'
    # test_data = '/Users/<USER>/Downloads/telefoneguiresponse_1749235056741.json'
    # test_data = '/Users/<USER>/Downloads/cnpjtbiz1response_1749233752058.json'
    # test_data = '/Users/<USER>/Downloads/4bf8a8a2-9748-400d-a411-ee06c2de75d6_794a643f-c18d-4ded-8643-65c40431dcd4 (1).json'
    # test_data = "/Users/<USER>/Downloads/123.json"
    # test_data = "/Users/<USER>/Downloads/cpf_report_pre_processing.json"
    test_data = "/Users/<USER>/Downloads/2newtest68000367654.json"
    # test_data = "/Users/<USER>/PycharmProjects/Snap_Reports_10/apps/spark/reports_processor/11045231673.json"
    # test_data = "/Users/<USER>/PycharmProjects/Snap_Reports_10/apps/spark/reports_processor/mock_retorno_snap_cpf_bembolado.json"

    with open(test_data, "r", encoding='utf-8') as f:
        file_content = f.read() # .decode('utf-8')

    processed_data = process_file(file_content)
    breakpoint()
