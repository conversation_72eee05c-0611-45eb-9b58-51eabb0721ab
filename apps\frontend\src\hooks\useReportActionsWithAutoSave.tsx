import { useCallback, useEffect } from "react";
import { toast } from "sonner";
import { useReportCRUD } from "~/hooks/useReportCRUD";
import { handleQueryError } from "~/helpers/errorHandling.helper";
import { useReportDetailStore } from "~/store/reportDetailStore";

type UpdateFnPromise = (entry: any, index?: number) => void;

let globalForceSave: (() => void) | null = null;

// Function to get the global force save function
export const getGlobalForceSave = () => globalForceSave;

export function useReportActionsWithAutoSave() {
  const actions = useReportDetailStore((state) => state.actions);
  const { addNewReportMutation,
    invalidateReportDetails,
    setReportDetailsToStale,
    resetReportListQuery
  } = useReportCRUD();

  const executeSave = useCallback(() => {
    const state = useReportDetailStore.getState();

    if (!state._autoSave.hasPendingChanges || state._autoSave.isPending) {
      return;
    }

    const { sections, metadata } = state;
    const reportId = metadata?.user_reports_id as string;

    const payload = {
      ...metadata,
      data: { [metadata?.report_type as string]: sections },
    };

    console.log("[useReportActionsWithAutoSave] Executando save com estado final:", payload);

    useReportDetailStore.setState((state) => ({
      ...state,
      _autoSave: {
        ...state._autoSave,
        isPending: true,
        hasPendingChanges: false,
        mutation: addNewReportMutation,
      }
    }));

    addNewReportMutation.mutate(payload, {
      onSuccess: () => {
        useReportDetailStore.setState((state) => ({
          ...state,
          _autoSave: {
            ...state._autoSave,
            isPending: false,
          }
        }));
        toast.success("Alterações salvas com sucesso!");
        if (reportId) {
          setReportDetailsToStale(reportId)  // marca como stale para refetch quando sair e entrar nos detalhes de novo
          resetReportListQuery() // TODO - por quanto estou fazendo refetch da lista para mostrar o card do report modificado primeiro - REVISAR
        };
      },
      onError: (error) => {
        useReportDetailStore.setState((state) => ({
          ...state,
          _autoSave: {
            ...state._autoSave,
            isPending: false,
            hasPendingChanges: false,
          }
        }));

        handleQueryError(error, {
          serverErrorMessage: "Falha ao salvar, tente novamente mais tarde.",
          title: "Erro ao salvar",
          onError: () => {
            if (reportId) {
              actions.forceReload(); // forçar o reload
            }
          }
        });
      },
    });
  }, [addNewReportMutation, invalidateReportDetails, actions]);

  // Set the global reference on first render
  useEffect(() => {
    globalForceSave = executeSave;
    return () => {
      globalForceSave = null;
    };
  }, [executeSave]);

  const updateSectionEntries = useCallback((
    sectionTitle: string,
    updaterFn: UpdateFnPromise,
    testEntryDeletedFn: (entry: any) => boolean,
    testSectionDeletedFn: (section: any) => boolean,
    calculateDataCountFn?: (section: any) => number,
    includeSubsections?: boolean,
    crossSectionUpdate?: { matchingProp: string; updaterFn: UpdateFnPromise }
  ) => {
    actions.updateSectionEntries(
      sectionTitle,
      updaterFn,
      testEntryDeletedFn,
      testSectionDeletedFn,
      calculateDataCountFn,
      includeSubsections,
      crossSectionUpdate
    );

    actions.scheduleAutoSave();
  }, [actions]);

  const updateSubsectionWithMainSection = useCallback((
    sectionTitle: string,
    subsectionName: string,
    matchingProp: string,
    updaterFn: UpdateFnPromise,
    testEntryDeletedFn: (entry: any) => boolean,
    testSectionDeletedFn: (section: any) => boolean,
    calculateDataCountFn?: (section: any) => number
  ) => {
    actions.updateSubsectionWithMainSection(
      sectionTitle,
      subsectionName,
      matchingProp,
      updaterFn,
      testEntryDeletedFn,
      testSectionDeletedFn,
      calculateDataCountFn
    );

    actions.scheduleAutoSave();
  }, [actions]);

  return {
    ...actions,
    updateSectionEntries,
    updateSubsectionWithMainSection,
    forceSave: executeSave,
  };
}

