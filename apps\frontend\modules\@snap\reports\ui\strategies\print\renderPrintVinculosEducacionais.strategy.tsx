import React from 'react';
import { View, Text, StyleSheet, Svg, Rect } from '@react-pdf/renderer';
import { ReportSection } from '../../../global';
import { translatePropToLabel, translateSource } from '../../../helpers';
import { ValueWithSource } from '../../../model/ValueWithSource';

interface RenderPrintVinculosEducacionais {
  section: Omit<ReportSection, 'data'> & {
    data: Array<{
      empresa_educadora?: {
        value: string;
        label: string;
        source: string[];
        is_deleted: boolean;
      };
      detalhes?: {
        [key: string]: {
          value: string;
          label: string;
          source: string[];
          is_deleted: boolean;
        }
      };
      [key: string]: any;
    }>
  };
}

export const RenderPrintVinculosEducacionais: React.FC<RenderPrintVinculosEducacionais> = ({ section }) => {
  if (!section.data?.length) return null;

  return (
    <View style={styles.container} key={section.title}>
      <View style={styles.sectionTitleContainer}>
        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
          <Rect width="8" height="8" fill='#FE473C' />
        </Svg>
        <Text style={styles.heading}>{section.title}</Text>
      </View>

      {section.data.map((vinculo, vinculoIndex) => (
        <View key={`vinculo-${vinculoIndex}`} style={styles.vinculoContainer}>
          {/* Empresa Educadora */}
          {vinculo.empresa_educadora && !vinculo.empresa_educadora.is_deleted && (
            <View style={styles.empresaContainer}>
              <View style={styles.empresaLabelContainer}>
                <Text style={styles.empresaLabel}>
                  {(vinculo.empresa_educadora.label || "Empresa Educadora").toUpperCase()}
                </Text>
                <Text style={styles.sourceText}>
                  {vinculo.empresa_educadora.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                </Text>
              </View>
              <Text style={styles.empresaValue}>{vinculo.empresa_educadora.value}</Text>
            </View>
          )}

          {/* Detalhes do Vínculo */}
          {vinculo.detalhes && (
            <View style={styles.detalhesContainer}>
              <View style={styles.grid}>
                {Object.entries(vinculo.detalhes as Record<string, ValueWithSource>)
                  .filter(([_, field]) => !field.is_deleted)
                  .map(([key, field], index) => (
                    <View key={`detalhe-${index}`} style={styles.detailsCell}>
                      <View style={styles.infoContainer}>
                        <Svg width={8} height={8} viewBox="0 0 8 8" style={styles.searchIcon}>
                          <Rect width="8" height="8" fill='#CCCCCC' />
                        </Svg>
                        <Text style={styles.label}>{translatePropToLabel(field.label || key).toUpperCase()}</Text>
                        <Text style={styles.sourceText}>
                          {field.source?.map((src: string) => `| ${translateSource(src)}\u00A0`).join('')}
                        </Text>
                      </View>
                      <Text style={styles.value}>{field.value}</Text>
                    </View>
                  ))}
              </View>
            </View>
          )}
        </View>
      ))}
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 8,
    marginBottom: 16,
  },
  heading: {
    fontSize: 12,
    textTransform: 'uppercase',
    marginBottom: 8,
    fontWeight: 'bold',
  },
  searchIcon: {
    width: 4,
    height: 4,
    marginRight: 4,
    marginTop: 1
  },
  sectionTitleContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    borderBottomWidth: 1,
    borderBottomColor: "#889EA3",
  },
  empresaLabelContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
    paddingBottom: 2,
  },
  sourceText: {
    paddingLeft: 4,
    paddingBottom: 2,
    fontSize: 8,
    color: '#FE473C',
  },
  empresaLabel: {
    fontSize: 10,
    fontWeight: 'bold',
    color: '#FE473C',
  },
  empresaValue: {
    fontSize: 12,
    fontWeight: 'bold',
  },
  vinculoContainer: {
    marginBottom: 16,
    padding: 8,
    backgroundColor: '#F9F9FA',
    borderBottomLeftRadius: 2,
    borderBottomRightRadius: 2,
  },
  empresaContainer: {
    marginBottom: 12,
  },
  detalhesContainer: {
    marginBottom: 12,
  },
  grid: {
    paddingLeft: 8,
    flexDirection: 'row',
    flexWrap: 'wrap',
  },
  detailsCell: {
    width: '50%',
    paddingRight: 8,
    marginBottom: 6,
  },
  label: {
    fontSize: 10,
    fontWeight: 'bold',
    marginBottom: 2,
    color: '#889EA3',
  },
  value: {
    paddingLeft: 8,
    fontSize: 10,
  },
  infoContainer: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    flexWrap: 'wrap',
  },
});
