import React from "react";
import { RenderStrategy } from "./RenderStrategy";
import { Input, ReadOnlyInputField, CustomLabel, GridContainer, GridItem } from "@snap/design-system";
import { parseValue, translatePropToLabel } from "../../helpers";
import { MdOutlineSubdirectoryArrowRight } from "react-icons/md";
import { PossivelPessoaRelacionada } from "../../model/PossiveisPessoasRelacionadas";
import { isValidArray, renderValidArray, renderSimpleArray, isValidUrl, renderSourceTooltip } from "./helpers.strategy";
import { ValueWithSource } from "../../model/ValueWithSource";
import { CustomReadOnlyInputField } from "../components/CustomReadOnlyInput";
import { ReportsCustomLabel } from "../components/ReportsCustomLabel";

export class RenderPossiveisPessoasRelacionadas implements RenderStrategy<PossivelPessoaRelacionada> {
  validateKeys = (keys: Array<keyof PossivelPessoaRelacionada>): boolean => {
    return keys.some((campo) => campo in this.formatByKey);
  };

  renderTelefones = (pessoa?: PossivelPessoaRelacionada): React.ReactElement | null => {
    return renderSimpleArray('telefones', pessoa || {}, 'phone number');
  };

  renderEnderecos = (pessoa?: PossivelPessoaRelacionada): React.ReactElement | null => {
    return renderValidArray('enderecos', pessoa || {});
  };

  formatByKey: Record<
    string,
    (pessoa?: PossivelPessoaRelacionada) => React.ReactElement | null
  > = {
      nome_completo: (pessoa?: PossivelPessoaRelacionada) => {
        if (!pessoa?.nome_completo) return null;

        return (
          <GridContainer cols={3}>
            <GridItem cols={1} className="mb-6 group">
              <CustomReadOnlyInputField
                label={(pessoa.nome_completo.label || "Nome Completo").toUpperCase()}
                colorClass="bg-primary"
                labelTextClass="text-accent"
                value={parseValue(pessoa.nome_completo.value)}
                tooltip={renderSourceTooltip(pessoa.nome_completo.source)}
              />
            </GridItem>
          </GridContainer>
        );
      },

      detalhes: (pessoa?: PossivelPessoaRelacionada) => {
        if (!pessoa?.detalhes?.length) return null;

        return (
          <GridContainer cols={2} columnFirst className="mb-6">
            {pessoa.detalhes.map((detalhe, index) => {
              const entries = Object.entries(detalhe.value as Record<string, ValueWithSource<string>> || {});
              return entries.map(([key, value]) => (
                <GridItem key={`${key}-${index}`} cols={1} className="group">
                  <CustomReadOnlyInputField
                    label={`${(translatePropToLabel(value.label || key)).toUpperCase()}`}
                    value={parseValue(String(value.value))}
                    icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                    tooltip={renderSourceTooltip(value.source)}
                  />
                </GridItem>
              ));
            })}
          </GridContainer>
        );
      },

      telefones: (pessoa?: PossivelPessoaRelacionada) => {
        return this.renderTelefones(pessoa);
      },

      imagens: (pessoa?: PossivelPessoaRelacionada) => {
        if (!pessoa?.imagens?.length) return null;

        return (
          <GridContainer cols={3} className="mb-6">
            <GridItem cols={3} >
              <ReportsCustomLabel label="IMAGENS" colorClass="bg-white" />
            </GridItem>

            <GridItem fullWidth>
              <GridContainer cols={2}>
                {pessoa.imagens.map((imagem, index) => (
                  <GridItem key={`imagem-${index}`} cols={1}>
                    <div className="">
                      {Object.entries(imagem.value).map(
                        ([key, valueObj]) => {
                          if (key === 'url' && valueObj && typeof valueObj === 'object') {
                            return (
                              <GridItem
                                key={`imagem-${index}-${key}`}
                                cols={1}
                                className="py-2 group"
                              >
                                <CustomReadOnlyInputField
                                  label={`${(valueObj.label || 'URL').toUpperCase()}`}
                                  value={String(valueObj.value || '')}
                                  tooltip={renderSourceTooltip(valueObj.source)}
                                />
                                {isValidUrl(String(valueObj.value)) && (
                                  <img
                                    src={String(valueObj.value)}
                                    alt={`Imagem ${index + 1}`}
                                    className="max-w-full h-auto pt-4"
                                  />
                                )}
                              </GridItem>
                            );
                          }
                          return null;
                        }
                      )}
                    </div>
                  </GridItem>
                ))}
              </GridContainer>
            </GridItem>

          </GridContainer>
        );
      },

      enderecos: (pessoa?: PossivelPessoaRelacionada) => {
        return this.renderEnderecos(pessoa);
      },

      redes_sociais: (pessoa?: PossivelPessoaRelacionada) => {
        if (!pessoa?.redes_sociais) return null;

        return (
          <>
            <GridContainer cols={3}>
              <GridItem cols={1} className="mb-6">
                <ReportsCustomLabel label="REDES SOCIAIS" colorClass="bg-white" />
              </GridItem>
            </GridContainer>
            <GridContainer cols={2}>
              {Object.entries(pessoa.redes_sociais).map(([label, valueObj]) => (
                <GridItem key={label} cols={1}>
                  <div className="mb-4 group">
                    <CustomReadOnlyInputField
                      label={(valueObj.label || "Rede Social").toUpperCase()}
                      value={""}
                      icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                      tooltip={renderSourceTooltip(valueObj.source)}
                    />
                  </div>
                  <div className="pl-5">
                    {Object.entries(valueObj.value as Record<string, ValueWithSource<string>> || {}).map(([key, value]) => (
                      <div className="py-2 group" key={key}>
                        <CustomReadOnlyInputField
                          label={translatePropToLabel(value.label || key).toUpperCase()}
                          value={parseValue(String(value.value))}
                          icon={<MdOutlineSubdirectoryArrowRight size={16} />}
                          tooltip={renderSourceTooltip(value.source)}
                        />
                      </div>
                    ))}
                  </div>
                </GridItem>
              ))}
            </GridContainer>
          </>
        );
      },
    };

  validateData = (pessoa?: PossivelPessoaRelacionada): boolean => {
    try {
      if (!pessoa || typeof pessoa !== 'object') {
        console.warn('Invalid pessoa data received:', pessoa);
        return false;
      }
      return true;
    } catch (error) {
      console.warn('Error validating pessoa data:', error);
      return false;
    }
  };

  render = (pessoa?: PossivelPessoaRelacionada): React.ReactElement[] => {
    try {
      if (!this.validateData(pessoa)) {
        console.warn('Invalid data structure received');
        return [];
      }

      const keys = Object.keys(pessoa!) as Array<keyof PossivelPessoaRelacionada>;

      if (!this.validateKeys(keys)) {
        console.warn('Invalid keys in data:', keys);
        return [];
      }

      return keys
        .map((chave) => {
          try {
            if (chave in this.formatByKey) {
              return this.formatByKey[chave]?.(pessoa);
            }

            const value = pessoa![chave];
            if (isValidArray(value)) {
              return renderValidArray(chave as string, pessoa!);
            }

            return null;
          } catch (error) {
            console.warn(`Error processing key ${chave}:`, error);
            return null;
          }
        })
        .filter((el): el is React.ReactElement => el !== null);
    } catch (error) {
      console.warn('Error rendering pessoa data:', error);
      return [];
    }
  };
}