import { useEffect, useState, useCallback } from "react";
import type { ReportDocumentProps } from "../ui/components/ReportDocument";
import { pdfGateway } from "../services";

export const useReportPDF = () => {
  const [state, setState] = useState<{
    loading: boolean;
    error: Error | undefined;
    url: string | undefined;
  }>({
    loading: false,
    error: undefined,
    url: undefined,
  });

  const generatePDF = useCallback(async (props: ReportDocumentProps) => {
    setState(prev => ({ ...prev, loading: true }));
    try {
      const url = await pdfGateway.generatePDF(props);
      setState({ loading: false, error: undefined, url });
      return url;
    } catch (error) {
      const err = error as Error;
      setState({ loading: false, error: err, url: undefined });
      throw err;
    }
  }, []);

  // Clean up the blob URL when component unmounts or URL changes
  useEffect(() => {
    return () => {
      if (state.url) {
        URL.revokeObjectURL(state.url);
      }
    };
  }, [state.url]);

  return {
    generatePDF,
    loading: state.loading,
    error: state.error,
    url: state.url
  };
};